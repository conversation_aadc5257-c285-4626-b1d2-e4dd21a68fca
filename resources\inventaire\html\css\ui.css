body {
    user-select: none;
    position: fixed;
}

* {
    padding: 0;
    margin: 0;
    color: #fff;
    font-family: 'SF Pro Display', sans-serif;                                          
}

.ui {
    position: relative;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(44, 44, 44, 0.486);
    opacity:1;
    display: block;
    z-index: 0
}


.buttoniconidcard{
    position: absolute;
    top:14vh;
    right:71.5vh;
}

.buttoniconidcard *{
    display: flex;
    opacity: 0.7;
}
.buttoniconidcard .raccours3:hover{
    opacity: 0.2;
}
.buttoniconidcard img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconidcard .raccours3:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconidcard > .raccours3 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours3 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours3:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours3:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours3 *{
  transform: rotate(90deg);
}
.raccours3:before {
    bottom: 0;
    height: 50%;
}
.raccours3 i{
    position: absolute;
    font-size: 4vh;
}


.raccourci2  {
    position: relative;
    top: 16.0vh;
    left: 6vh;
    display: table;
}
.raccours10,
.raccours11 {
    font-weight: bold;
    font-size: 3.3vh;
}

.autourname{
    font-weight: bold;
    font-size: 3.3vh;
}
.raccours10:hover {
    opacity: 0.7;
}
.autourname:hover {
    opacity: 0.7;
}

.buttoniconbag{
    position: absolute;
    top:14vh;
    right:93.5vh;
}

.buttoniconbag *{
    display: flex;
    opacity: 0.7;
}
.buttoniconbag .raccours1:hover{
    opacity: 0.2;
}
.buttoniconbag img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconbag .raccours1:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconbag > .raccours1 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours1 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours1:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours1:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours1 *{
  transform: rotate(90deg);
}
.raccours1:before {
    bottom: 0;
    height: 50%;
}
.raccours1 i{
    position: absolute;
    font-size: 5vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}


.buttoniconvtm{
    position: absolute;
    top:14vh;
    right:82.5vh;
}

.buttoniconvtm *{
    display: flex;
    opacity: 0.7;
}
.buttoniconvtm .raccours2:hover{
    opacity: 0.2;
}
.buttoniconvtm img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconvtm .raccours2:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconvtm > .raccours2 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours2 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours2:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours2:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours2 *{
  transform: rotate(90deg);
}
.raccours2:before {
    bottom: 0;
    height: 50%;
}
.raccours2 i{
    position: absolute;
    font-size: 5vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}


.inventory {
    position: absolute;
    left: 50vw;
    top: 50vh;
    transform: translate(-50%, -50%);
    width: 93.5vw;
    height: 60vh;
    color: rgba(255, 255, 255, 0.158);
    border-radius: 2vh
}

#playerInventory {
    width: 31vw;
    height: 70vh;
    float: left;
    overflow-y: auto;
    background-color: rgba(17,17,17,0.319);
}

#playerInventoryFastItems {
    width: 70vw;
    height: 5vh;
    position: absolute;
    top: 72vh;
    left: 0;
    z-index: 100;
    float: left
}

#otherInventory {
    width: 50vh;
    height: 70vh;
    float: right;
    background-color: rgba(17,17,17,0.319);
}

#count {
    border: none;
    outline: 0;
    font-size: 1.8vh;
}

input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input[type=number] {
    -moz-appearance: textfield
}

.slot {
    float: left;
    width: 9.2vh;
    height: 9.2vh;
    top: 1.4vw;
    color: #fff;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    margin: 0.5vh;
    border-radius: 3%;
    position: relative;
}

.slotfix {
    float: left;
    width: 9.2vh;
    height: 9.2vh;
    top: 1.4vw;
    color: #fff;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    margin: 0.3vh;
    border-radius: 3%;
    position: relative
}

.slot:hover {
    opacity: 0.8;
}

.item-box2 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.item-box2:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.item-box2:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.item-box2 *{
  transform: rotate(90deg);
}
.item-box2:before {
    bottom: 0;
    height: 50%;
}
.item-box2 i{
    position: absolute;
    font-size: 4vh;

}
.cloth-items2{
    position: absolute;
    top:29vh;
    right:35vw;
}

.cloth-items2 *{
    display: flex;
    opacity: 0.7;
}
.cloth-items2 .item-box2:hover{
    opacity: 0.2;
}
.cloth-items2 img{
    width: 70%;
    height: 70%;
    right: 1vh;
}
.cloth-items2 .item-box2:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.cloth-items2 > .item-box2 > span{
    position: relative;
    right:1.9vh;
    font-size: 1.8vh;
}

.item-box {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.item-box:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.item-box:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.item-box *{
  transform: rotate(90deg);
}
.item-box:before {
    bottom: 0;
    height: 50%;
}
.item-box i{
    position: absolute;
    font-size: 4vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}
.cloth-items{
    position: absolute;
    top:29vh;
    right:57.5vw;
}

.cloth-items *{
    display: flex;
    opacity: 0.7;
}
.cloth-items .item-box:hover{
    opacity: 0.2;
}

.cloth-items img{
    width: 70%;
    height: 70%;
    right: 1vh;
}

.cloth-items .item-box:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.cloth-items > .item-box > span{
    position: relative;
    right:1.9vh;
    font-size: 1.8vh;
}

.slotFast {
    float: left;
    width: 9.2vh;
    height: 9.2vh;
    bottom: 250%;
    left: 64vh;
    color: #fff;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    margin: 0.5vh;
    border-radius: 3%;
    position: relative
}

.slotFast:hover {
    opacity: 0.8;
}

.item,
.item-other {
    width: 8vh;
    height: 6vh;
    margin-top: 1vh;
    margin-left: 0.7vh;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center
}

.item-name {
    position: absolute;
    bottom: 0;
    background-color: rgba(39, 49, 68, 0.315);
    font-family: 'Roboto', sans-serif;
    font-size: 0.94vh;
    font-weight: bold;
    text-align: center;
    padding-top: 0.3vh;
    padding-bottom: 0.8vh;
    margin-left: -0.7vh;
    width: 9.4vh;
    min-height: 0.94vh;
    z-index: 500
}

.ui-draggable-dragging .item-count,
.ui-draggable-dragging .item-name {
    display: none
}

.item-count {
    position: absolute;
    text-align: left;
    font-family: 'Roboto', sans-serif;
    font-weight: bold;
    width: 2vh;
    margin-top: -0.9vh;
    margin-left: 0vh;
    height: 2vh;
    color: rgb(189, 189, 189);
    z-index: 500;
    font-size: 0.9vh
}

.keybind {
    position: absolute;
    text-align: right;
    width: 11.4vh;
    margin-top: -15px;
    margin-left: -100px;
    height: 20px;
    z-index: 500;
    font-size: 0vh
}

.ammoIcon {
    width: 8px;
    height: 8px
}

.info-div {
    text-align: left;
    padding: 5px;
    width: 174px;
    position: absolute;
    font-size: 1.2vh;
    left: 64%;
    top: 1%;
    transform: translate(-50%, -50%)
}

.info-div2 {
    text-align: left;
    padding: 5px;
    width: 155px;
    position: absolute;
    font-size: 1.2vh;
    left: 30%;
    top: 1%;
    transform: translate(-50%, -50%)
}

.controls-div {
}

.control {
    position: absolute;
    width: 10vh;
    height: 3vh;
    background-color: rgba(255, 255, 255, 0);
    margin: 0.5vh;
    text-align: center;
    font-size: 0.94vh;
    top: 59vh;
    left: 0vh;
}
.control2 {
    position: absolute;
    width: 6vh;
    height: 3vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 0.3vh;
    font-size: 2vh;
    text-align: center;
    top: 59.5vh;
    left: 9vh;
}

.control3 {
    position: absolute;
    width: 6vh;
    height: 3vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 0.3vh;
    margin: 3.75vh;
    text-align: center;
    font-size: 2vh;
    top: 55.8vh;
    left: 12vh;
}


.control.disabled {
    background-color: rgba(36, 36, 36, 0.479);
}

.hoverControl {
    background-color: rgba(36, 36, 36, 0.479);
}

/* .used-item-slot {
    position: absolute;
    right: 50vh;
    bottom: 50vh;
    height: 125px;
    width: 110px;
    background-color: rgba(114, 114, 114, 0.308);
    border-style: solid;
    border-color: rgba(255, 255, 255, .2);
    border-width: 1px 1px 1px 1px
} */

/* #used-item-image {
    height: 12.5vh;
    width: 11vh;
    background-image: url(img/used-item.png);
    background-repeat: no-repeat;
    background-size: 0%;
    background-position-x: 0
}


#added-item-image {
    height: 125px;
    width: 110px;
    background-image: url(img/added-item.png);
    background-repeat: no-repeat;
    background-size: 80%;
    background-position-x: 0
} */
/* 
.item-name-bg {
    width: 100%;
    width: 117px;
    height: 20px;
    position: absolute;
    bottom: 0
} */

#otherInventory::-webkit-scrollbar-track,
#playerInventory::-webkit-scrollbar-track {
    background-color: none;
    border: none
}

#controlstrunk {
    width: 35vh;
    height: 50vh;
    float: left;
    position: relative;
    left: 62vh;
    top: -2.2vw;
}


.weighttrunk-div {
    text-align: left;
    padding: 0.5vh;
    width: 15.5vh;   
    position: absolute;
    font-size: 1.3vh;
    left: 22vw;
    top: 7%;
    transform: translate(-50%, -50%)
}


#otherInventory::-webkit-scrollbar,
#playerInventory::-webkit-scrollbar {
    width: 1vh
}

.nearbyPlayerButton {
    width: 100vh;
    margin-top: 0.5vh;
    display: block;
    text-decoration: none;
    padding: 0.2vh;
    color: rgba(255, 255, 255, .85);
    background-color: rgba(49, 49, 49, 0.274);
    border-radius: 9vh;
    text-shadow: none;
    font-size: 1.4vh!important;
    outline: 0;
    text-transform: none;
    text-align: center;
    line-height: 3vh;
    border: none
}

.nearbyPlayerButton:hover {
    background-color: rgba(36, 36, 36, 0.2)
}

#noSecondInventoryMessage {
    width: 61.5vh;
    height: 58vh;
    line-height: 58vh;
    text-align: center
}
/* 
@media (max-width: 100vh) {
    .inventory {
        width: 14vh;
    }

    #playerInventory {
        width: 600px;
        height: 500px;
        bottom: 105px;
    }

    #controls {
        width: 200px;
    }

    .control {
        width: 190px;
        height: 40px;
        left: 500px;
    }

    .raccours {
        top: 14%;
        right: -2%;
    }

    .info-div {
        padding: 15px;
        width: 174px;
        left: 105%;
        top: 0.5%;
    }

    .slotFast {
        width: 120px;
        height: 120px;
        bottom: 450%;
    }

    #otherInventory {
        width: 600px;
        height: 500px;
    }

    #noSecondInventoryMessage {
        width: 540px;
    }
} */